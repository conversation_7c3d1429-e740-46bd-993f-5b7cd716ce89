package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.ResultMonitorMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.ResultMonitorBean;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.component.FileComparisonComponent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ResultMonitorServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ResultMonitorServiceImplTest {

    @Mock
    private ResultMonitorMapper resultMonitorMapper;
    
    @Mock
    private RunFlowResultMapper runFlowResultMapper;
    
    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private FileComparisonComponent fileComparisonComponent;

    @InjectMocks
    private ResultMonitorServiceImpl resultMonitorService;

    private ResultMonitorQueryDto queryDto;
    private ResultMonitorBean monitorBean;
    private RunFlowResultEntity flowResult;
    private RunFlowEntity runFlow;
    private RunRuleEntity runRule;

    @BeforeEach
    void setUp() {
        // 初始化查询DTO
        queryDto = new ResultMonitorQueryDto();
        queryDto.setBusinessSystemName("testSystem");
        queryDto.setModel(RuleModelEnum.COMPARE.getCode());
        queryDto.setResult(1);
        queryDto.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        // 初始化监控Bean
        monitorBean = new ResultMonitorBean();
        monitorBean.setId(1L);
        monitorBean.setCreateTime(new Timestamp(System.currentTimeMillis() - 1000));
        monitorBean.setElapsedTime(1000L);
        monitorBean.setFrom(StartFromEnums.MANUAL_TRIGGER.getCode());

        // 初始化流程结果实体
        flowResult = new RunFlowResultEntity();
        flowResult.setContent("source@$@target");
        flowResult.setStderr("error message");

        // 初始化流程实体
        runFlow = new RunFlowEntity();
        runFlow.setRunBizId(1L);

        // 初始化规则实体
        runRule = new RunRuleEntity();
        runRule.setSourcePath("/source/path");
        runRule.setPath("/target/path");
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 正常场景")
    void testSelectResultMonitorList_Normal() {
        // 准备测试数据
        List<ResultMonitorBean> beanList = Arrays.asList(monitorBean);
        doReturn(beanList).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), anyInt());

        // 执行测试
        PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        verify(resultMonitorMapper).selectResultMonitorList(
            queryDto.getBusinessSystemName(),
            queryDto.getModel(),
            queryDto.getResult(),
            queryDto.getFrom()
        );
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 查询条件为空")
    void testSelectResultMonitorList_NullQuery() {
        // 执行测试
        PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(null, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        verify(resultMonitorMapper, never()).selectResultMonitorList(any(), any(), any(), any());
    }

    @Test
    @DisplayName("测试查询比对结果列表 - 模型为空")
    void testSelectResultMonitorList_NullModel() {
        // 准备测试数据
        queryDto.setModel(null);
        List<ResultMonitorBean> beanList = Arrays.asList(monitorBean);
        doReturn(beanList).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), eq(RuleModelEnum.COMPARE.getCode()), anyInt(), anyInt());

        // 执行测试
        PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        assertEquals(RuleModelEnum.COMPARE.getCode(), queryDto.getModel());
    }

    @Test
    @DisplayName("测试查询比对结果列表 - from为空")
    void testSelectResultMonitorList_NullFrom() {
        // 准备测试数据
        queryDto.setFrom(null);
        List<ResultMonitorBean> beanList = Arrays.asList(monitorBean);
        doReturn(beanList).when(resultMonitorMapper).selectResultMonitorList(
            anyString(), anyInt(), anyInt(), isNull());

        // 执行测试
        PageInfo<ResultMonitorDto> result = resultMonitorService.selectResultMonitorList(queryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        verify(resultMonitorMapper).selectResultMonitorList(
            queryDto.getBusinessSystemName(),
            queryDto.getModel(),
            queryDto.getResult(),
            null
        );
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 正常场景")
    void testSelectContentDetailByFlowId_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试
        ContentDetailDto result = resultMonitorService.selectContentDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("source", result.getSourceContent());
        assertEquals("target", result.getTargetContent());
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 流程ID为空")
    void testSelectContentDetailByFlowId_NullFlowId() {
        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentDetailByFlowId(null));
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 未找到结果")
    void testSelectContentDetailByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentDetailByFlowId(1L));
    }

    @Test
    @DisplayName("测试根据流程ID查询比对详情 - 使用stderr内容")
    void testSelectContentDetailByFlowId_UseStderr() throws ContrastBusinessException {
        // 准备测试数据
        flowResult.setContent(null);
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试
        ContentDetailDto result = resultMonitorService.selectContentDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowResult.getStderr(), result.getSourceContent());
        assertEquals("", result.getTargetContent());
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 正常场景")
    void testSelectContentForCompareFileByFlowId_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());
        doReturn(runFlow).when(runFlowMapper).selectRunFlowByFlowId(anyLong());
        doReturn(runRule).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试
        String result = resultMonitorService.selectContentForCompareFileByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(runFlowMapper).selectRunFlowByFlowId(1L);
        verify(runRuleMapper).selectRunRuleById(1L);
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 流程ID为空")
    void testSelectContentForCompareFileByFlowId_NullFlowId() {
        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            resultMonitorService.selectContentForCompareFileByFlowId(null));
    }

    @Test
    @DisplayName("测试查询文件比对内容 - 未找到结果")
    void testSelectContentForCompareFileByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.selectContentForCompareFileByFlowId(1L)
        );
        
        assertEquals("未查询到对应的流程结果数据", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 正常场景JSON格式")
    void testExportComparisonReportByFlowId_Normal_JsonFormat() throws ContrastBusinessException {
        // 准备测试数据
        String jsonContent = "{\"sourceContent\":\"source file content\",\"targetContent\":\"target file content\",\"content\":\"comparison result\",\"ret\":true}";
        flowResult.setContent(jsonContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, "基线服务器", "目标服务器", mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                eq("source file content"),
                eq("target file content"),
                eq("基线服务器"),
                eq("目标服务器"),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 正常场景分隔符格式")
    void testExportComparisonReportByFlowId_Normal_SeparatorFormat() throws ContrastBusinessException {
        // 准备测试数据
        String separatorContent = "source content@$@target content";
        flowResult.setContent(separatorContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, "基线服务器", "目标服务器", mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                eq("source content"),
                eq("target content"),
                eq("基线服务器"),
                eq("目标服务器"),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 流程ID为空")
    void testExportComparisonReportByFlowId_NullFlowId() {
        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(null, "基线服务器", "目标服务器", mockResponse)
        );

        assertEquals("flowId不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 未找到结果")
    void testExportComparisonReportByFlowId_NoResult() {
        // 准备测试数据
        doReturn(null).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, "基线服务器", "目标服务器", mockResponse)
        );

        assertEquals("未查询到对应的流程结果数据", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 内容为空")
    void testExportComparisonReportByFlowId_EmptyContent() {
        // 准备测试数据
        flowResult.setContent(null);
        flowResult.setStderr(null);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> resultMonitorService.exportComparisonReportByFlowId(1L, "基线服务器", "目标服务器", mockResponse)
        );

        assertEquals("流程结果内容为空，无法导出比对报表", exception.getMessage());
    }

    @Test
    @DisplayName("测试导出比对报表 - 使用stderr内容")
    void testExportComparisonReportByFlowId_UseStderrContent() throws ContrastBusinessException {
        // 准备测试数据
        flowResult.setContent(null);
        flowResult.setStderr("stderr content only");

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, "基线服务器", "目标服务器", mockResponse);

        // 验证结果
        verify(runFlowResultMapper).selectRunFlowResultByFlowId(1L);
        verify(fileComparisonComponent).compareAndExport(
                eq("stderr content only"),
                eq(""),
                eq("基线服务器"),
                eq("目标服务器"),
                eq(mockResponse)
        );
    }

    @Test
    @DisplayName("测试导出比对报表 - 服务器名称为空时使用默认值")
    void testExportComparisonReportByFlowId_DefaultServerNames() throws ContrastBusinessException {
        // 准备测试数据
        String jsonContent = "{\"sourceContent\":\"source content\",\"targetContent\":\"target content\"}";
        flowResult.setContent(jsonContent);

        doReturn(flowResult).when(runFlowResultMapper).selectRunFlowResultByFlowId(anyLong());

        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行测试
        resultMonitorService.exportComparisonReportByFlowId(1L, null, null, mockResponse);

        // 验证结果
        verify(fileComparisonComponent).compareAndExport(
                eq("source content"),
                eq("target content"),
                eq("基线服务器"),
                eq("目标服务器"),
                eq(mockResponse)
        );
    }
}