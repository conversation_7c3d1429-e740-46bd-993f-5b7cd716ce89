package com.ideal.envc.service;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件比较服务接口
 *
 * <AUTHOR>
 */
public interface IFileComparisonService {

    /**
     * 比较两个文件内容字符串
     *
     * @param request 比较请求参数
     * @return 比较结果
     */
    FileComparisonResultDto compareFileContents(FileComparisonRequestDto request) throws ContrastBusinessException;

    /**
     * 导出比较结果到Excel
     *
     * @param request 比较请求参数
     * @param response HTTP响应对象
     */
    void exportComparisonResult(FileComparisonRequestDto request, HttpServletResponse response) throws ContrastBusinessException;

    /**
     * 导出已有比较结果到Excel
     *
     * @param result 比较结果
     * @param response HTTP响应对象
     */
    void exportComparisonResult(FileComparisonResultDto result, HttpServletResponse response,FileComparisonRequestDto request) throws ContrastBusinessException;

    /**
     * 导出比较结果到Excel（带IP和主机名信息）
     *
     * @param result 比较结果
     * @param sourceIp 源IP地址
     * @param sourceHostname 源主机名
     * @param targetIp 目标IP地址
     * @param targetHostname 目标主机名
     * @param response HTTP响应对象
     */
    void exportComparisonResult(FileComparisonResultDto result, String sourceIp, String sourceHostname,
                               String targetIp, String targetHostname, HttpServletResponse response) throws ContrastBusinessException;
}
