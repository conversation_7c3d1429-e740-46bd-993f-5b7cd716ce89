package com.ideal.envc.component;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.service.impl.FileComparisonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 主机名查询功能集成测试
 *
 * <AUTHOR>
 */
public class HostnameQueryIntegrationTest {

    private FileComparisonComponent fileComparisonComponent;
    private SystemComputerMapper systemComputerMapper;
    private String sourceContent;
    private String targetContent;

    @BeforeEach
    void setUp() {
        // 创建Mock的SystemComputerMapper
        systemComputerMapper = Mockito.mock(SystemComputerMapper.class);
        
        // 创建真实的服务实例
        FileComparisonServiceImpl fileComparisonService = new FileComparisonServiceImpl();
        
        // 创建组件实例
        fileComparisonComponent = new FileComparisonComponent(fileComparisonService, systemComputerMapper);

        // 准备测试数据
        sourceContent = "file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)\n" +
                       "file2.txt (size: 2048, permissions: -rw-rw-r--, MD5: def456)";
        
        targetContent = "file1.txt (size: 1024, permissions: -rw-r--r--, MD5: abc123)\n" +
                       "file3.txt (size: 512, permissions: -rw-r--r--, MD5: ghi789)";
    }

    @Test
    @DisplayName("集成测试 - IP查询主机名成功的完整流程")
    void testCompleteFlow_HostnameQuerySuccess() throws ContrastBusinessException {
        System.out.println("=== 开始IP查询主机名成功的集成测试 ===");
        
        // 模拟主机名查询成功
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn("A-ECIF-APP01");
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn("B-ECIF-APP02");

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行比较并导出
        assertDoesNotThrow(() -> {
            fileComparisonComponent.compareAndExport(
                    sourceContent, targetContent,
                    "*************", "*************",
                    response);
        });

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证响应
        assertNotNull(response.getContentType());
        assertTrue(response.getContentLength() > 0);

        System.out.println("✅ 主机名查询成功，Excel导出完成");
        System.out.println("  - 基线服务器：************* → A-ECIF-APP01");
        System.out.println("  - 目标服务器：************* → B-ECIF-APP02");
        System.out.println("  - Content-Type: " + response.getContentType());
        System.out.println("  - Content-Length: " + response.getContentLength());
        System.out.println("=== IP查询主机名成功的集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - IP查询主机名失败的降级处理")
    void testCompleteFlow_HostnameQueryFallback() throws ContrastBusinessException {
        System.out.println("=== 开始IP查询主机名失败的集成测试 ===");
        
        // 模拟主机名查询失败
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn(null);
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn("");

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行比较并导出 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            fileComparisonComponent.compareAndExport(
                    sourceContent, targetContent,
                    "*************", "*************",
                    response);
        });

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证响应
        assertNotNull(response.getContentType());
        assertTrue(response.getContentLength() > 0);

        System.out.println("✅ 主机名查询失败，使用IP作为主机名，Excel导出完成");
        System.out.println("  - 基线服务器：************* → *************（降级）");
        System.out.println("  - 目标服务器：************* → *************（降级）");
        System.out.println("  - Content-Type: " + response.getContentType());
        System.out.println("  - Content-Length: " + response.getContentLength());
        System.out.println("=== IP查询主机名失败的集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - IP查询异常的异常处理")
    void testCompleteFlow_HostnameQueryException() throws ContrastBusinessException {
        System.out.println("=== 开始IP查询异常的集成测试 ===");
        
        // 模拟主机名查询异常
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenThrow(new RuntimeException("数据库连接异常"));
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn("B-ECIF-APP02");

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行比较并导出 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            fileComparisonComponent.compareAndExport(
                    sourceContent, targetContent,
                    "*************", "*************",
                    response);
        });

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证响应
        assertNotNull(response.getContentType());
        assertTrue(response.getContentLength() > 0);

        System.out.println("✅ 主机名查询异常，使用异常处理机制，Excel导出完成");
        System.out.println("  - 基线服务器：************* → *************（异常降级）");
        System.out.println("  - 目标服务器：************* → B-ECIF-APP02（正常查询）");
        System.out.println("  - Content-Type: " + response.getContentType());
        System.out.println("  - Content-Length: " + response.getContentLength());
        System.out.println("=== IP查询异常的集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - 空IP地址的处理")
    void testCompleteFlow_EmptyIpAddress() throws ContrastBusinessException {
        System.out.println("=== 开始空IP地址的集成测试 ===");
        
        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行比较并导出 - 使用空IP地址
        assertDoesNotThrow(() -> {
            fileComparisonComponent.compareAndExport(
                    sourceContent, targetContent,
                    "", null,
                    response);
        });

        // 验证主机名查询不会被调用（因为IP为空）
        verify(systemComputerMapper, never()).selectComputerNameByIp(anyString());

        // 验证响应
        assertNotNull(response.getContentType());
        assertTrue(response.getContentLength() > 0);

        System.out.println("✅ 空IP地址处理完成，Excel导出完成");
        System.out.println("  - 基线服务器：\"\" → \"\"（空IP不查询）");
        System.out.println("  - 目标服务器：null → null（null IP不查询）");
        System.out.println("  - Content-Type: " + response.getContentType());
        System.out.println("  - Content-Length: " + response.getContentLength());
        System.out.println("=== 空IP地址的集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - 批量查询功能验证")
    void testBatchHostnameQuery() {
        System.out.println("=== 开始批量查询功能验证 ===");
        
        // 准备批量查询的测试数据
        SystemComputerEntity computer1 = new SystemComputerEntity();
        computer1.setComputerIp("*************");
        computer1.setComputerName("A-ECIF-APP01");
        
        SystemComputerEntity computer2 = new SystemComputerEntity();
        computer2.setComputerIp("*************");
        computer2.setComputerName("B-ECIF-APP02");
        
        Map<String, SystemComputerEntity> batchResult = new HashMap<>();
        batchResult.put("*************", computer1);
        batchResult.put("*************", computer2);

        // 模拟批量查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(anyList()))
                .thenReturn(batchResult);

        // 验证批量查询方法存在且可以被调用
        Map<String, SystemComputerEntity> result = systemComputerMapper.selectComputerNameMapByIps(
                java.util.Arrays.asList("*************", "*************"));

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("A-ECIF-APP01", result.get("*************").getComputerName());
        assertEquals("B-ECIF-APP02", result.get("*************").getComputerName());

        System.out.println("✅ 批量查询功能验证完成");
        System.out.println("  - 查询结果数量：" + result.size());
        System.out.println("  - ************* → " + result.get("*************").getComputerName());
        System.out.println("  - ************* → " + result.get("*************").getComputerName());
        System.out.println("=== 批量查询功能验证完成 ===");
    }
}
