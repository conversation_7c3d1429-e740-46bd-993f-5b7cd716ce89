# 文件比较组件使用说明

## 概述

文件比较组件（FileComparisonComponent）是一个用于处理两个字符串（文件列表信息）的比较和汇总功能的组件。该组件以源内容（sourceContent）为基线，分析目标内容（targetContent）与源内容相比的差异，包括新增、缺失、不一致和一致的文件，并支持导出Excel报告。

## 功能特性

- **文件内容比较**：以MD5值为准判断文件是否一致
- **差异分析**：识别一致、不一致、缺失、多出的文件
- **统计汇总**：提供各类文件的数量和比率统计
- **Excel导出**：支持将比较结果导出为Excel文件
- **灵活调用**：提供组件、服务、控制器多层次的调用方式

## 输入格式

输入的字符串格式如下：
```
COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)
LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87e0e91a6dbc15f)
README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)
bin/java (size: 8.27 KB, permissions: -rw-r--r--, MD5: 9d5432654f4567e4e5076e6498471e8b)
```

每行包含：
- 文件路径/名称
- 文件大小（size: xxx）
- 文件权限（permissions: xxx）
- MD5值（MD5: xxx）

## 比较逻辑

### 基本分类
- **一致**：源和目标都存在，且根据比较策略判断为一致
- **不一致**：源和目标都存在，但根据比较策略判断为不一致
- **缺失**：源存在但目标不存在
- **多出**：目标存在但源不存在

### 比较策略

#### 1. MD5_ONLY（仅MD5比较）
- **判断标准**：只比较MD5值
- **一致条件**：MD5值相同
- **适用场景**：只关心文件内容是否相同

#### 2. COMPREHENSIVE（综合比较）
- **判断标准**：比较文件大小、权限和MD5值
- **一致条件**：文件大小、权限、MD5值都相同
- **适用场景**：需要严格一致性检查，确保文件的所有属性都相同

### 策略选择建议
- **默认策略**：MD5_ONLY（向后兼容）
- **严格检查**：使用COMPREHENSIVE策略
- **灵活配置**：可根据业务需求动态选择

## 使用方式

### 1. 组件方式调用（推荐）

```java
@Autowired
private FileComparisonComponent fileComparisonComponent;

// 基本比较
FileComparisonResultDto result = fileComparisonComponent.compareFiles(sourceContent, targetContent);

// 带服务器信息的比较
FileComparisonResultDto result = fileComparisonComponent.compareFiles(
    sourceContent, targetContent, "基线服务器", "目标服务器");

// 使用指定比较策略的比较
FileComparisonResultDto result = fileComparisonComponent.compareFiles(
    sourceContent, targetContent, FileComparisonStrategy.COMPREHENSIVE);

// 带服务器信息和比较策略的比较
FileComparisonResultDto result = fileComparisonComponent.compareFiles(
    sourceContent, targetContent, "基线服务器", "目标服务器",
    FileComparisonStrategy.COMPREHENSIVE);

// 比较并导出Excel
fileComparisonComponent.compareAndExport(sourceContent, targetContent, response);

// 使用指定比较策略的比较并导出
fileComparisonComponent.compareAndExport(
    sourceContent, targetContent,
    "192.168.1.100", "基线服务器",
    "192.168.1.101", "目标服务器",
    FileComparisonStrategy.COMPREHENSIVE,
    response);

// 获取比较摘要
String summary = fileComparisonComponent.getComparisonSummary(result);
```

### 2. 服务层调用

```java
@Autowired
private IFileComparisonService fileComparisonService;

FileComparisonRequestDto request = new FileComparisonRequestDto();
request.setSourceContent(sourceContent);
request.setTargetContent(targetContent);
request.setBaselineServer("基线服务器");
request.setTargetServer("目标服务器");

FileComparisonResultDto result = fileComparisonService.compareFileContents(request);
```

### 3. REST API调用

#### 比较文件内容
```http
POST /fileComparison/compare
Content-Type: application/json

{
    "sourceContent": "文件内容字符串...",
    "targetContent": "文件内容字符串...",
    "baselineServer": "基线服务器",
    "targetServer": "目标服务器",
    "description": "比较描述"
}
```

#### 导出Excel
```http
POST /fileComparison/export
Content-Type: application/json

{
    "sourceContent": "文件内容字符串...",
    "targetContent": "文件内容字符串...",
    "baselineServer": "基线服务器",
    "targetServer": "目标服务器"
}
```

#### 快速比较（返回摘要）
```http
POST /fileComparison/quickCompare
Content-Type: application/json

{
    "sourceContent": "文件内容字符串...",
    "targetContent": "文件内容字符串..."
}
```

## 返回结果

### FileComparisonResultDto 结构

```json
{
    "baselineServer": "基线服务器",
    "targetServer": "目标服务器",
    "description": "比较描述",
    "totalSourceFiles": 100,
    "totalTargetFiles": 95,
    "consistentCount": 80,
    "inconsistentCount": 10,
    "missingCount": 10,
    "extraCount": 5,
    "consistentRate": 80.00,
    "inconsistentRate": 10.00,
    "missingRate": 10.00,
    "extraRate": 5.26,
    "consistentFiles": [...],
    "inconsistentFiles": [...],
    "missingFiles": [...],
    "extraFiles": [...]
}
```

### Excel导出格式

导出的Excel文件包含以下列：
- 文件路径
- 状态（一致/不一致/缺失/多出）
- 基线大小
- 目标大小
- 基线MD5
- 目标MD5
- 权限
- 备注

## 权限配置

使用REST API需要配置相应的权限：
```java
@MethodPermission("@dp.hasBtnPermission('file-comparison')")
```

## 注意事项

1. **输入验证**：确保输入的字符串格式正确，每行都包含完整的文件信息
2. **内存使用**：大量文件比较时注意内存使用情况
3. **MD5比较**：文件一致性完全基于MD5值判断
4. **字符编码**：确保输入字符串使用UTF-8编码
5. **Excel导出**：导出的Excel文件名包含时间戳，避免重名

## 示例代码

```java
@RestController
public class ExampleController {
    
    @Autowired
    private FileComparisonComponent fileComparisonComponent;
    
    @PostMapping("/example/compare")
    public R<String> exampleCompare(@RequestBody Map<String, String> request) {
        String sourceContent = request.get("sourceContent");
        String targetContent = request.get("targetContent");
        
        // 验证输入
        if (!fileComparisonComponent.validateInput(sourceContent, targetContent)) {
            return R.fail("输入参数无效");
        }
        
        // 执行比较
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
            sourceContent, targetContent, "服务器A", "服务器B");
        
        // 获取摘要
        String summary = fileComparisonComponent.getComparisonSummary(result);
        
        return R.ok(summary);
    }
}
```

## 错误处理

组件会抛出 `ContrastBusinessException` 异常，包含详细的错误信息：
- 文件内容解析失败
- Excel导出失败
- 参数验证失败

建议在调用时进行适当的异常处理。
