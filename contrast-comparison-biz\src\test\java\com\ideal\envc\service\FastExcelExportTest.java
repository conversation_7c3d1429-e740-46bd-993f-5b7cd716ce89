package com.ideal.envc.service;

import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.service.impl.FileComparisonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FastExcel导出功能测试
 *
 * <AUTHOR>
 */
public class FastExcelExportTest {

    private FileComparisonServiceImpl fileComparisonService;
    private String sourceContent;
    private String targetContent;

    @BeforeEach
    void setUp() {
        fileComparisonService = new FileComparisonServiceImpl();

        // 准备测试数据
        sourceContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\n" +
                       "README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)";

        targetContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: different_md5_value)\n" +
                       "new_file.txt (size: 500.00 B, permissions: -rw-r--r--, MD5: new_file_md5_value)";
    }

    @Test
    @DisplayName("测试FastExcel导出功能")
    void testFastExcelExport() {
        // 准备请求
        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);
        request.setBaselineServer("基线服务器");
        request.setTargetServer("目标服务器");

        // 创建模拟响应
        MockHttpServletResponse mockResponse = new MockHttpServletResponse();

        // 执行导出
        assertDoesNotThrow(() -> {
            fileComparisonService.exportComparisonResult(request, mockResponse);
        }, "Excel导出不应该抛出异常");

        // 验证响应设置
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                mockResponse.getContentType(), "Content-Type应该正确设置");
        assertEquals("utf-8", mockResponse.getCharacterEncoding(), "字符编码应该正确设置");
        
        // 验证是否有数据写入
        assertTrue(mockResponse.getOutputStreamSize() > 0, "应该有数据写入到输出流");
        
        System.out.println("✅ FastExcel导出测试通过");
        System.out.println("📋 导出信息：");
        System.out.println("├─ Content-Type: " + mockResponse.getContentType());
        System.out.println("├─ Character-Encoding: " + mockResponse.getCharacterEncoding());
        System.out.println("└─ 导出数据大小: " + mockResponse.getOutputStreamSize() + " bytes");
    }

    @Test
    @DisplayName("测试Excel导出异常处理")
    void testExcelExportExceptionHandling() {
        // 准备无效请求
        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(null);
        request.setTargetContent(null);

        // 创建会抛出异常的响应
        HttpServletResponse errorResponse = new HttpServletResponse() {
            @Override
            public ServletOutputStream getOutputStream() throws IOException {
                throw new IOException("模拟IO异常");
            }

            // 其他方法的简单实现
            @Override public void addCookie(javax.servlet.http.Cookie cookie) {}
            @Override public boolean containsHeader(String name) { return false; }
            @Override public String encodeURL(String url) { return url; }
            @Override public String encodeRedirectURL(String url) { return url; }
            @Override public String encodeUrl(String url) { return url; }
            @Override public String encodeRedirectUrl(String url) { return url; }
            @Override public void sendError(int sc, String msg) throws IOException {}
            @Override public void sendError(int sc) throws IOException {}
            @Override public void sendRedirect(String location) throws IOException {}
            @Override public void setDateHeader(String name, long date) {}
            @Override public void addDateHeader(String name, long date) {}
            @Override public void setHeader(String name, String value) {}
            @Override public void addHeader(String name, String value) {}
            @Override public void setIntHeader(String name, int value) {}
            @Override public void addIntHeader(String name, int value) {}
            @Override public void setStatus(int sc) {}
            @Override public void setStatus(int sc, String sm) {}
            @Override public int getStatus() { return 200; }
            @Override public String getHeader(String name) { return null; }
            @Override public Collection<String> getHeaders(String name) { return null; }
            @Override public Collection<String> getHeaderNames() { return null; }
            @Override public PrintWriter getWriter() throws IOException { return null; }
            @Override public void setContentType(String type) {}
            @Override public String getContentType() { return null; }
            @Override public void setContentLength(int len) {}
            @Override public void setContentLengthLong(long len) {}
            @Override public void setCharacterEncoding(String charset) {}
            @Override public String getCharacterEncoding() { return null; }
            @Override public void setBufferSize(int size) {}
            @Override public int getBufferSize() { return 0; }
            @Override public void flushBuffer() throws IOException {}
            @Override public void resetBuffer() {}
            @Override public boolean isCommitted() { return false; }
            @Override public void reset() {}
            @Override public Locale getLocale() { return Locale.getDefault(); }
            @Override public void setLocale(Locale loc) {}
        };

        // 验证异常处理
        assertThrows(Exception.class, () -> {
            fileComparisonService.exportComparisonResult(request, errorResponse);
        }, "应该正确处理IO异常");

        System.out.println("✅ Excel导出异常处理测试通过");
    }

    /**
     * 模拟HttpServletResponse
     */
    static class MockHttpServletResponse implements HttpServletResponse {
        private String contentType;
        private String characterEncoding;
        private ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        @Override
        public void setContentType(String type) {
            this.contentType = type;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public void setCharacterEncoding(String charset) {
            this.characterEncoding = charset;
        }

        @Override
        public String getCharacterEncoding() {
            return characterEncoding;
        }

        @Override
        public ServletOutputStream getOutputStream() throws IOException {
            return new ServletOutputStream() {
                @Override
                public boolean isReady() {
                    return true;
                }

                @Override
                public void setWriteListener(javax.servlet.WriteListener writeListener) {
                }

                @Override
                public void write(int b) throws IOException {
                    outputStream.write(b);
                }

                @Override
                public void write(byte[] b) throws IOException {
                    outputStream.write(b);
                }

                @Override
                public void write(byte[] b, int off, int len) throws IOException {
                    outputStream.write(b, off, len);
                }
            };
        }

        public int getOutputStreamSize() {
            return outputStream.size();
        }

        // 其他方法的简单实现
        @Override public void addCookie(javax.servlet.http.Cookie cookie) {}
        @Override public boolean containsHeader(String name) { return false; }
        @Override public String encodeURL(String url) { return url; }
        @Override public String encodeRedirectURL(String url) { return url; }
        @Override public String encodeUrl(String url) { return url; }
        @Override public String encodeRedirectUrl(String url) { return url; }
        @Override public void sendError(int sc, String msg) throws IOException {}
        @Override public void sendError(int sc) throws IOException {}
        @Override public void sendRedirect(String location) throws IOException {}
        @Override public void setDateHeader(String name, long date) {}
        @Override public void addDateHeader(String name, long date) {}
        @Override public void setHeader(String name, String value) {}
        @Override public void addHeader(String name, String value) {}
        @Override public void setIntHeader(String name, int value) {}
        @Override public void addIntHeader(String name, int value) {}
        @Override public void setStatus(int sc) {}
        @Override public void setStatus(int sc, String sm) {}
        @Override public int getStatus() { return 200; }
        @Override public String getHeader(String name) { return null; }
        @Override public Collection<String> getHeaders(String name) { return null; }
        @Override public Collection<String> getHeaderNames() { return null; }
        @Override public PrintWriter getWriter() throws IOException { return new PrintWriter(outputStream); }
        @Override public void setContentLength(int len) {}
        @Override public void setContentLengthLong(long len) {}
        @Override public void setBufferSize(int size) {}
        @Override public int getBufferSize() { return 0; }
        @Override public void flushBuffer() throws IOException {}
        @Override public void resetBuffer() {}
        @Override public boolean isCommitted() { return false; }
        @Override public void reset() {}
        @Override public Locale getLocale() { return Locale.getDefault(); }
        @Override public void setLocale(Locale loc) {}
    }
}
