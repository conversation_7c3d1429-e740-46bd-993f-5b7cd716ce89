package com.ideal.envc.component;

import com.ideal.envc.model.enums.FileComparisonStrategy;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.service.IFileComparisonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件比较组件单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class FileComparisonComponentTest {

    @Mock
    private IFileComparisonService fileComparisonService;

    @Mock
    private SystemComputerMapper systemComputerMapper;

    @InjectMocks
    private FileComparisonComponent fileComparisonComponent;

    private String sourceContent;
    private String targetContent;
    private FileComparisonResultDto mockResult;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        sourceContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\n" +
                       "README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)";

        targetContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: different_md5_value)\n" +
                       "NEW_FILE (size: 100.00 B, permissions: -rw-r--r--, MD5: new_file_md5_value)";

        // 准备模拟结果
        mockResult = new FileComparisonResultDto();
        mockResult.setTotalSourceFiles(3);
        mockResult.setTotalTargetFiles(3);
        mockResult.setConsistentCount(1);
        mockResult.setInconsistentCount(1);
        mockResult.setMissingCount(1);
        mockResult.setExtraCount(1);
    }

    @Test
    @DisplayName("测试基本文件比较功能")
    void testCompareFiles_Basic() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(sourceContent, targetContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalSourceFiles());
        assertEquals(3, result.getTotalTargetFiles());
        assertEquals(1, result.getConsistentCount());
        assertEquals(1, result.getInconsistentCount());
        assertEquals(1, result.getMissingCount());
        assertEquals(1, result.getExtraCount());
    }

    @Test
    @DisplayName("测试带服务器信息的文件比较功能")
    void testCompareFiles_WithServerInfo() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalSourceFiles());
        assertEquals(3, result.getTotalTargetFiles());
    }

    @Test
    @DisplayName("测试完整参数的文件比较功能")
    void testCompareFiles_WithFullParams() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器", "测试比较");

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalSourceFiles());
        assertEquals(3, result.getTotalTargetFiles());
    }

    @Test
    @DisplayName("测试输入参数验证 - 源内容为空")
    void testValidateInput_SourceContentEmpty() {
        // 执行测试
        boolean result = fileComparisonComponent.validateInput("", targetContent);

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试输入参数验证 - 目标内容为空")
    void testValidateInput_TargetContentEmpty() {
        // 执行测试
        boolean result = fileComparisonComponent.validateInput(sourceContent, "");

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试输入参数验证 - 内容为null")
    void testValidateInput_ContentNull() {
        // 执行测试
        boolean sourceNullResult = fileComparisonComponent.validateInput(null, targetContent);
        boolean targetNullResult = fileComparisonComponent.validateInput(sourceContent, null);

        // 验证结果
        assertFalse(sourceNullResult);
        assertFalse(targetNullResult);
    }

    @Test
    @DisplayName("测试输入参数验证 - 有效输入")
    void testValidateInput_ValidInput() {
        // 执行测试
        boolean result = fileComparisonComponent.validateInput(sourceContent, targetContent);

        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("测试比较结果摘要生成")
    void testGetComparisonSummary() {
        // 执行测试
        String summary = fileComparisonComponent.getComparisonSummary(mockResult);

        // 验证结果
        assertNotNull(summary);
        assertTrue(summary.contains("基线文件总数：3"));
        assertTrue(summary.contains("目标文件总数：3"));
        assertTrue(summary.contains("一致文件：1个"));
        assertTrue(summary.contains("不一致文件：1个"));
        assertTrue(summary.contains("缺失文件：1个"));
        assertTrue(summary.contains("多出文件：1个"));
    }

    @Test
    @DisplayName("测试比较结果摘要生成 - 结果为null")
    void testGetComparisonSummary_NullResult() {
        // 执行测试
        String summary = fileComparisonComponent.getComparisonSummary(null);

        // 验证结果
        assertEquals("比较结果为空", summary);
    }

    @Test
    @DisplayName("测试带比较策略的文件比较功能")
    void testCompareFiles_WithComparisonStrategy() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, FileComparisonStrategy.COMPREHENSIVE);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalSourceFiles());
        assertEquals(3, result.getTotalTargetFiles());

        // 验证传递给服务层的请求参数
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).compareFileContents(requestCaptor.capture());

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, capturedRequest.getComparisonStrategy());
        assertEquals(sourceContent, capturedRequest.getSourceContent());
        assertEquals(targetContent, capturedRequest.getTargetContent());
    }

    @Test
    @DisplayName("测试带服务器信息和比较策略的文件比较功能")
    void testCompareFiles_WithServerInfoAndStrategy() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器",
                FileComparisonStrategy.COMPREHENSIVE);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.getTotalSourceFiles());
        assertEquals(3, result.getTotalTargetFiles());

        // 验证传递给服务层的请求参数
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).compareFileContents(requestCaptor.capture());

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, capturedRequest.getComparisonStrategy());
        assertEquals("基线服务器", capturedRequest.getBaselineServer());
        assertEquals("目标服务器", capturedRequest.getTargetServer());
    }

    @Test
    @DisplayName("测试带比较策略的比较并导出功能")
    void testCompareAndExport_WithComparisonStrategy() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);
        doNothing().when(fileComparisonService).exportComparisonResult(
                any(FileComparisonResultDto.class), anyString(), anyString(), anyString(), anyString(), any(HttpServletResponse.class));

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试
        fileComparisonComponent.compareAndExport(
                sourceContent, targetContent,
                "*************", "基线服务器",
                "*************", "目标服务器",
                FileComparisonStrategy.COMPREHENSIVE,
                response);

        // 验证比较服务被调用
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).compareFileContents(requestCaptor.capture());

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals(FileComparisonStrategy.COMPREHENSIVE, capturedRequest.getComparisonStrategy());
        assertEquals("基线服务器", capturedRequest.getBaselineServer());
        assertEquals("目标服务器", capturedRequest.getTargetServer());

        // 验证导出服务被调用
        verify(fileComparisonService).exportComparisonResult(
                eq(mockResult), eq("*************"), eq("基线服务器"),
                eq("*************"), eq("目标服务器"), eq(response));
    }

    @Test
    @DisplayName("测试比较策略为null时的处理")
    void testCompareFiles_WithNullStrategy() throws ContrastBusinessException {
        // 模拟服务层返回
        when(fileComparisonService.compareFileContents(any())).thenReturn(mockResult);

        // 执行测试 - 传入null策略
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, null);

        // 验证结果
        assertNotNull(result);

        // 验证传递给服务层的请求参数
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).compareFileContents(requestCaptor.capture());

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        // null策略应该被设置为默认的MD5_ONLY
        assertEquals(FileComparisonStrategy.MD5_ONLY, capturedRequest.getComparisonStrategy());
    }

    @Test
    @DisplayName("测试输入验证 - 带比较策略的方法")
    void testValidateInput_WithComparisonStrategy() {
        // 测试空源内容
        assertThrows(ContrastBusinessException.class, () -> {
            fileComparisonComponent.compareFiles("", targetContent, FileComparisonStrategy.COMPREHENSIVE);
        });

        // 测试空目标内容
        assertThrows(ContrastBusinessException.class, () -> {
            fileComparisonComponent.compareFiles(sourceContent, "", FileComparisonStrategy.COMPREHENSIVE);
        });

        // 测试null内容
        assertThrows(ContrastBusinessException.class, () -> {
            fileComparisonComponent.compareFiles(null, targetContent, FileComparisonStrategy.COMPREHENSIVE);
        });

        assertThrows(ContrastBusinessException.class, () -> {
            fileComparisonComponent.compareFiles(sourceContent, null, FileComparisonStrategy.COMPREHENSIVE);
        });
    }

    @Test
    @DisplayName("测试带IP参数的比较并导出功能 - 查询到主机名")
    void testCompareAndExport_WithIpParameters_HostnameFound() throws ContrastBusinessException {
        // 模拟主机名查询结果
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenReturn("A-ECIF-APP01");
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenReturn("B-ECIF-APP02");

        // 模拟服务层返回
        doNothing().when(fileComparisonService).exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试
        fileComparisonComponent.compareAndExport(
                sourceContent, targetContent,
                "*************", "*************",
                response);

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证导出服务被调用
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).exportComparisonResult(requestCaptor.capture(), eq(response));

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals("*************", capturedRequest.getBaseServerIp());
        assertEquals("*************", capturedRequest.getTargetServerIp());
        assertEquals("A-ECIF-APP01", capturedRequest.getBaselineServer());
        assertEquals("B-ECIF-APP02", capturedRequest.getTargetServer());
    }

    @Test
    @DisplayName("测试带IP参数的比较并导出功能 - 查询不到主机名")
    void testCompareAndExport_WithIpParameters_HostnameNotFound() throws ContrastBusinessException {
        // 模拟主机名查询结果为空
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenReturn(null);
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenReturn("");

        // 模拟服务层返回
        doNothing().when(fileComparisonService).exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试
        fileComparisonComponent.compareAndExport(
                sourceContent, targetContent,
                "*************", "*************",
                response);

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证导出服务被调用，当查询不到主机名时，使用IP地址作为主机名
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).exportComparisonResult(requestCaptor.capture(), eq(response));

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals("*************", capturedRequest.getBaseServerIp());
        assertEquals("*************", capturedRequest.getTargetServerIp());
        assertEquals("*************", capturedRequest.getBaselineServer()); // 使用IP作为主机名
        assertEquals("*************", capturedRequest.getTargetServer()); // 使用IP作为主机名
    }

    @Test
    @DisplayName("测试带IP参数的比较并导出功能 - 查询异常")
    void testCompareAndExport_WithIpParameters_QueryException() throws ContrastBusinessException {
        // 模拟主机名查询异常
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenThrow(new RuntimeException("数据库连接异常"));
        when(systemComputerMapper.selectComputerNameByIp("*************")).thenReturn("B-ECIF-APP02");

        // 模拟服务层返回
        doNothing().when(fileComparisonService).exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试 - 应该不抛出异常，而是使用IP作为主机名
        assertDoesNotThrow(() -> {
            fileComparisonComponent.compareAndExport(
                    sourceContent, targetContent,
                    "*************", "*************",
                    response);
        });

        // 验证主机名查询被调用
        verify(systemComputerMapper).selectComputerNameByIp("*************");
        verify(systemComputerMapper).selectComputerNameByIp("*************");

        // 验证导出服务被调用，异常情况下使用IP地址作为主机名
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).exportComparisonResult(requestCaptor.capture(), eq(response));

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals("*************", capturedRequest.getBaseServerIp());
        assertEquals("*************", capturedRequest.getTargetServerIp());
        assertEquals("*************", capturedRequest.getBaselineServer()); // 异常时使用IP作为主机名
        assertEquals("B-ECIF-APP02", capturedRequest.getTargetServer()); // 正常查询到的主机名
    }

    @Test
    @DisplayName("测试带空IP参数的比较并导出功能")
    void testCompareAndExport_WithEmptyIpParameters() throws ContrastBusinessException {
        // 模拟服务层返回
        doNothing().when(fileComparisonService).exportComparisonResult(any(FileComparisonRequestDto.class), any(HttpServletResponse.class));

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试 - 使用空字符串IP
        fileComparisonComponent.compareAndExport(
                sourceContent, targetContent,
                "", null,
                response);

        // 验证主机名查询不会被调用（因为IP为空）
        verify(systemComputerMapper, never()).selectComputerNameByIp(anyString());

        // 验证导出服务被调用
        ArgumentCaptor<FileComparisonRequestDto> requestCaptor = ArgumentCaptor.forClass(FileComparisonRequestDto.class);
        verify(fileComparisonService).exportComparisonResult(requestCaptor.capture(), eq(response));

        FileComparisonRequestDto capturedRequest = requestCaptor.getValue();
        assertEquals("", capturedRequest.getBaseServerIp());
        assertNull(capturedRequest.getTargetServerIp());
        assertEquals("", capturedRequest.getBaselineServer()); // 空IP对应空主机名
        assertNull(capturedRequest.getTargetServer()); // null IP对应null主机名
    }
}
