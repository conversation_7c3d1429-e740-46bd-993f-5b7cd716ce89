2025-07-24 00:10:42.252 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 00:20:42.244 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 01:00:42.308 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 01:20:42.300 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 01:50:42.368 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 02:20:42.350 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 02:40:42.419 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 03:20:42.410 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 03:30:42.462 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 04:20:42.459 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 04:20:42.514 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.837 [DubboMetadataReportTimer-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:434 -  [DUBBO] start to publish all metadata., dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.841 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@79a510f4; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.system.api.IBusinessSystemCompuerList, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=system, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753265443105}, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.843 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@7f038931; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.system.api.ICenter, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=system, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753265440664}, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.844 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@244562af; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.engine.api.IStartFlow, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=engine, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=killFlow,pauseFlow,resumeFlow,startFlow, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753265442275}, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.844 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@553236c8; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.system.api.IBusinessSystem, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=system, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753265442833}, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.845 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@3343ff0d; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.system.api.IRoleProjectPermission, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=system, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getRoleButtonAuthority, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753265442006}, dubbo version: 3.2.5, current host: **********
2025-07-24 04:42:37.846 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@3c2fa2d0; definition: {pid=23344, side=consumer, version=1.0.0, interface=com.ideal.engine.api.IActivity, application=contrast-dubbo, release=3.2.5, dubbo=2.0.2, group=engine, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753265442563}, dubbo version: 3.2.5, current host: **********
2025-07-24 05:10:42.564 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 05:20:42.512 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 06:00:42.604 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 06:20:42.561 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 06:50:42.644 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 07:20:42.623 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 07:40:42.689 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 08:20:42.682 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 08:30:42.735 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 08:32:59.941 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x0cd59a1b, L:/**********:64580 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-24 08:32:59.969 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x0cd59a1b, L:/**********:64580 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.075 [NettyClientWorker-9-2] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:64580 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.499 [nacos-grpc-client-executor-************-19704] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.499 [nacos-grpc-client-executor-************-19704] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753257619232"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:33:00.499 [nacos-grpc-client-executor-************-19704] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-24 08:33:00.500 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@298ff16
2025-07-24 08:33:00.501 [nacos-grpc-client-executor-************-19724] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.501 [nacos-grpc-client-executor-************-19724] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753257617998"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:33:00.502 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.502 [nacos-grpc-client-executor-************-19724] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-24 08:33:00.502 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.503 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.505 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-24 08:33:00.524 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.526 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.526 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.526 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-24 08:33:00.527 [nacos-grpc-client-executor-************-19704] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.530 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.530 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.531 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c0aecafc
2025-07-24 08:33:00.531 [nacos-grpc-client-executor-************-19724] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.535 [nacos-grpc-client-executor-************-19725] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.536 [nacos-grpc-client-executor-************-19725] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753257619232"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:33:00.537 [nacos-grpc-client-executor-************-19725] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-24 08:33:00.544 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c1349b66
2025-07-24 08:33:00.547 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-24 08:33:00.547 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=23344&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753265442275&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.550 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-24 08:33:00.550 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.550 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=23344&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753265442563&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.564 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x0cd59a1b, L:/**********:64580 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-24 08:33:00.572 [nacos-grpc-client-executor-************-19725] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:33:00.576 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.776 [nacos-grpc-client-executor-************-19748] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.777 [nacos-grpc-client-executor-************-19748] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753317237778"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.776 [nacos-grpc-client-executor-************-19727] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.777 [nacos-grpc-client-executor-************-19748] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753317237778"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.777 [nacos-grpc-client-executor-************-19727] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753317239015"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.777 [nacos-grpc-client-executor-************-19727] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753317239015"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.778 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@e842633e
2025-07-24 08:34:01.778 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=23344&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753265442275&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.786 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@298ff16
2025-07-24 08:34:01.786 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.787 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.787 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.794 [nacos-grpc-client-executor-************-19727] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.795 [nacos-grpc-client-executor-************-19748] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.804 [NettyClientWorker-9-3] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:50588 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.804 [nacos-grpc-client-executor-************-19749] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.805 [nacos-grpc-client-executor-************-19749] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753317239015"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.806 [nacos-grpc-client-executor-************-19749] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753317239015"},"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000}]
2025-07-24 08:34:01.807 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xe4c08065, L:/**********:50588 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.807 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.823 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.826 [nacos-grpc-client-executor-************-19749] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-24 08:34:01.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.837 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.837 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.838 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.838 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.840 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.846 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.846 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.847 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@54949f90
2025-07-24 08:34:01.847 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=23344&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753265442563&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.851 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-24 08:34:01.851 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-24 09:20:42.736 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-24 09:20:42.774 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-24 09:54:34.146 [nacos-grpc-client-executor-************-21541] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Receive server push request, request = ClientDetectionRequest, requestId = ***********-07-24 09:54:34.175 [nacos-grpc-client-executor-************-21541] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Ack server push request, request = ClientDetectionRequest, requestId = ***********-07-24 09:54:34.176 [nacos-grpc-client-executor-************-21541] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266508883_**********_64663]Request stream onCompleted, switch server
2025-07-24 09:54:34.714 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Server healthy check fail, currentConnection = 1753266508883_**********_64663
2025-07-24 09:54:34.716 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:54:34.740 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:54:35.138 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Success to connect a server [************:8848], connectionId = 1753322078502_**********_52408
2025-07-24 09:54:35.138 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Abandon prev connection, server is ************:8848, connectionId is 1753266508883_**********_64663
2025-07-24 09:54:35.138 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266508883_**********_64663
2025-07-24 09:54:35.139 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:54:35.139 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:54:35.140 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify disconnected event to listeners
2025-07-24 09:54:35.140 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:54:35.140 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:54:35.142 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify connected event to listeners.
2025-07-24 09:54:35.142 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:54:35.412 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Success to connect a server [************:8848], connectionId = 1753322078786_**********_52411
2025-07-24 09:54:35.412 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Abandon prev connection, server is ************:8848, connectionId is 1753322078502_**********_52408
2025-07-24 09:54:35.412 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322078502_**********_52408
2025-07-24 09:54:35.413 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify disconnected event to listeners
2025-07-24 09:54:35.414 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:54:35.414 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:54:35.523 [nacos-grpc-client-executor-************-21557] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322078502_**********_52408]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:54:35.414 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify connected event to listeners.
2025-07-24 09:54:35.527 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:54:37.151 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:73 - Redo instance operation REGISTER for DEFAULT_GROUP@@contrast
2025-07-24 09:54:37.192 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@contrast#
2025-07-24 09:54:37.818 [nacos-grpc-client-executor-************-21562] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Receive server push request, request = NotifySubscriberRequest, requestId = 329897
2025-07-24 09:54:37.850 [nacos-grpc-client-executor-************-21562] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Ack server push request, request = NotifySubscriberRequest, requestId = 329897
2025-07-24 09:55:36.257 [nacos-grpc-client-executor-************-21622] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 329899
2025-07-24 09:55:36.257 [nacos-grpc-client-executor-************-21622] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 329899
2025-07-24 09:55:36.258 [nacos-grpc-client-executor-************-21622] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266508801_**********_64661]Request stream onCompleted, switch server
2025-07-24 09:55:36.257 [nacos-grpc-client-executor-************-21640] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 329898
2025-07-24 09:55:36.257 [nacos-grpc-client-executor-************-21639] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 329903
2025-07-24 09:55:36.261 [nacos-grpc-client-executor-************-21640] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 329898
2025-07-24 09:55:36.261 [nacos-grpc-client-executor-************-21640] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266508925_**********_64664]Request stream onCompleted, switch server
2025-07-24 09:55:36.261 [nacos-grpc-client-executor-************-21639] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 329903
2025-07-24 09:55:36.262 [nacos-grpc-client-executor-************-21639] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266509007_**********_64665]Request stream onCompleted, switch server
2025-07-24 09:55:36.280 [nacos-grpc-client-executor-************-21571] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Receive server push request, request = ClientDetectionRequest, requestId = 329901
2025-07-24 09:55:36.280 [nacos-grpc-client-executor-************-21571] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Ack server push request, request = ClientDetectionRequest, requestId = 329901
2025-07-24 09:55:36.280 [nacos-grpc-client-executor-************-21571] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753322078786_**********_52411]Request stream onCompleted, switch server
2025-07-24 09:55:36.280 [nacos-grpc-client-executor-************-21566] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = ClientDetectionRequest, requestId = 329900
2025-07-24 09:55:36.281 [nacos-grpc-client-executor-************-21566] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = ClientDetectionRequest, requestId = 329900
2025-07-24 09:55:36.281 [nacos-grpc-client-executor-************-21566] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266508797_**********_64660]Request stream onCompleted, switch server
2025-07-24 09:55:36.299 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = ClientDetectionRequest, requestId = ***********-07-24 09:55:36.299 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = ClientDetectionRequest, requestId = ***********-07-24 09:55:36.299 [nacos-grpc-client-executor-************-21590] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753266508822_**********_64662]Request stream onCompleted, switch server
2025-07-24 09:55:36.336 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Server healthy check fail, currentConnection = 1753322078786_**********_52411
2025-07-24 09:55:36.339 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.339 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.405 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Server healthy check fail, currentConnection = 1753266509007_**********_64665
2025-07-24 09:55:36.406 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.406 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Success to connect a server [************:8848], connectionId = 1753322139920_**********_58761
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Abandon prev connection, server is ************:8848, connectionId is 1753322078786_**********_52411
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322078786_**********_52411
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify disconnected event to listeners
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:36.441 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:36.441 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify connected event to listeners.
2025-07-24 09:55:36.441 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:36.440 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.441 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.475 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Success to connect a server [************:8848], connectionId = 1753322139996_**********_58763
2025-07-24 09:55:36.475 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753266509007_**********_64665
2025-07-24 09:55:36.475 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266509007_**********_64665
2025-07-24 09:55:36.477 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.478 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.478 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Notify disconnected event to listeners
2025-07-24 09:55:36.478 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] DisConnected,clear listen context...
2025-07-24 09:55:36.478 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Notify connected event to listeners.
2025-07-24 09:55:36.478 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Connected,notify listen context...
2025-07-24 09:55:36.504 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Success to connect a server [************:8848], connectionId = 1753322140028_**********_58764
2025-07-24 09:55:36.538 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Success to connect a server [************:8848], connectionId = 1753322140068_**********_58765
2025-07-24 09:55:36.616 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Server healthy check fail, currentConnection = 1753266508801_**********_64661
2025-07-24 09:55:36.674 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Abandon prev connection, server is ************:8848, connectionId is 1753322139920_**********_58761
2025-07-24 09:55:36.674 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.674 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322139920_**********_58761
2025-07-24 09:55:36.674 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.627 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Server healthy check fail, currentConnection = 1753266508797_**********_64660
2025-07-24 09:55:36.675 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.676 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.767 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify disconnected event to listeners
2025-07-24 09:55:36.769 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:36.769 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:36.769 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Notify connected event to listeners.
2025-07-24 09:55:36.769 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:36.674 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753322139996_**********_58763
2025-07-24 09:55:36.769 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322139996_**********_58763
2025-07-24 09:55:36.771 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Notify disconnected event to listeners
2025-07-24 09:55:36.772 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] DisConnected,clear listen context...
2025-07-24 09:55:36.772 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Notify connected event to listeners.
2025-07-24 09:55:36.772 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [ebbbd915-8384-4c28-a7ab-daedcdc70c44_config-0] Connected,notify listen context...
2025-07-24 09:55:36.772 [nacos-grpc-client-executor-************-21585] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322139920_**********_58761]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:36.777 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Server healthy check fail, currentConnection = 1753266508822_**********_64662
2025-07-24 09:55:36.778 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:36.778 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:36.859 [nacos-grpc-client-executor-************-21656] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322139996_**********_58763]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:37.191 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Server healthy check fail, currentConnection = 1753266508925_**********_64664
2025-07-24 09:55:37.231 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:37.321 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Success to connect a server [************:8848], connectionId = 1753322140567_**********_58771
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Abandon prev connection, server is ************:8848, connectionId is 1753266508797_**********_64660
2025-07-24 09:55:37.321 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Success to connect a server [************:8848], connectionId = 1753322140566_**********_58772
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266508797_**********_64660
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:37.331 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Success to connect a server [************:8848], connectionId = 1753322140639_**********_58773
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Abandon prev connection, server is ************:8848, connectionId is 1753266508822_**********_64662
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266508822_**********_64662
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:37.720 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753266508801_**********_64661
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266508801_**********_64661
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Notify disconnected event to listeners
2025-07-24 09:55:37.722 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Notify disconnected event to listeners
2025-07-24 09:55:37.721 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Notify disconnected event to listeners
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] DisConnected,clear listen context...
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Notify connected event to listeners.
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Notify connected event to listeners.
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:37.732 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:37.732 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Notify connected event to listeners.
2025-07-24 09:55:37.731 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Connected,notify listen context...
2025-07-24 09:55:37.732 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:37.957 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Success to connect a server [************:8848], connectionId = 1753322141335_**********_58774
2025-07-24 09:55:37.957 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753266508925_**********_64664
2025-07-24 09:55:37.957 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753266508925_**********_64664
2025-07-24 09:55:37.958 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 09:55:37.959 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Success to connect a server [************:8848], connectionId = 1753322141408_**********_58777
2025-07-24 09:55:37.959 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-24 09:55:37.959 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Abandon prev connection, server is ************:8848, connectionId is 1753322140566_**********_58772
2025-07-24 09:55:37.959 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322140566_**********_58772
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Notify disconnected event to listeners
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Notify disconnected event to listeners
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] DisConnected,clear listen context...
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Notify connected event to listeners.
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Connected,notify listen context...
2025-07-24 09:55:37.989 [nacos-grpc-client-executor-************-21607] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322140566_**********_58772]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:37.991 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Success to connect a server [************:8848], connectionId = 1753322141408_**********_58776
2025-07-24 09:55:37.991 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753322140639_**********_58773
2025-07-24 09:55:37.991 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322140639_**********_58773
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Notify disconnected event to listeners
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Success to connect a server [************:8848], connectionId = 1753322141333_**********_58775
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] DisConnected,clear listen context...
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Abandon prev connection, server is ************:8848, connectionId is 1753322140567_**********_58771
2025-07-24 09:55:37.960 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Notify connected event to listeners.
2025-07-24 09:55:37.994 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [9618799e-884d-4e67-b140-8d8c6b97c622_config-0] Connected,notify listen context...
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Notify connected event to listeners.
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322140567_**********_58771
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Notify disconnected event to listeners
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Notify connected event to listeners.
2025-07-24 09:55:37.995 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-24 09:55:38.043 [nacos-grpc-client-executor-************-21585] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322140567_**********_58771]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:38.044 [nacos-grpc-client-executor-************-21641] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322140639_**********_58773]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:38.072 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Success to connect a server [************:8848], connectionId = 1753322141610_**********_58779
2025-07-24 09:55:38.072 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753322141335_**********_58774
2025-07-24 09:55:38.072 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753322141335_**********_58774
2025-07-24 09:55:38.073 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Notify disconnected event to listeners
2025-07-24 09:55:38.073 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] DisConnected,clear listen context...
2025-07-24 09:55:38.073 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Notify connected event to listeners.
2025-07-24 09:55:38.073 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [31e13cf8-f1bc-4917-a0db-0bda86436a26_config-0] Connected,notify listen context...
2025-07-24 09:55:38.076 [nacos-grpc-client-executor-************-21659] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753322141335_**********_58774]Ignore error event,isRunning:true,isAbandon=true
2025-07-24 09:55:39.240 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:73 - Redo instance operation REGISTER for DEFAULT_GROUP@@contrast
2025-07-24 09:55:39.248 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@contrast#
2025-07-24 09:55:39.254 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-system#
2025-07-24 09:55:39.255 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo#
2025-07-24 09:55:39.265 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-engine#
2025-07-24 09:55:39.268 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system#
2025-07-24 09:55:39.278 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-system-lei#
2025-07-24 09:55:39.280 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine#
2025-07-24 09:55:39.289 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system#
2025-07-24 09:55:39.293 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system#
2025-07-24 09:55:39.303 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine#
2025-07-24 09:55:39.308 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system#
2025-07-24 09:55:39.794 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329906
2025-07-24 09:55:39.795 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329906
2025-07-24 09:55:39.801 [nacos-grpc-client-executor-************-21591] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329908
2025-07-24 09:55:39.804 [nacos-grpc-client-executor-************-21591] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329908
2025-07-24 09:55:39.805 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329911
2025-07-24 09:55:39.806 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329911
2025-07-24 09:55:39.809 [nacos-grpc-client-executor-************-21592] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = NotifySubscriberRequest, requestId = 329905
2025-07-24 09:55:39.810 [nacos-grpc-client-executor-************-21592] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = NotifySubscriberRequest, requestId = 329905
2025-07-24 09:55:39.815 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329913
2025-07-24 09:55:39.816 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329913
2025-07-24 09:55:39.816 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Receive server push request, request = NotifySubscriberRequest, requestId = 329912
2025-07-24 09:55:39.816 [nacos-grpc-client-executor-************-21590] INFO  com.alibaba.nacos.common.remote.client:63 - [b3ad7fb8-6ae0-4b0a-b80d-768d43fbcb4c] Ack server push request, request = NotifySubscriberRequest, requestId = 329912
2025-07-24 09:55:39.829 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = NotifySubscriberRequest, requestId = 329909
2025-07-24 09:55:39.830 [nacos-grpc-client-executor-************-21593] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = NotifySubscriberRequest, requestId = 329909
2025-07-24 09:55:39.845 [nacos-grpc-client-executor-************-21594] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Receive server push request, request = NotifySubscriberRequest, requestId = 329910
2025-07-24 09:55:39.846 [nacos-grpc-client-executor-************-21594] INFO  com.alibaba.nacos.common.remote.client:63 - [4325d700-9c75-4e29-bb35-61e32f129baa] Ack server push request, request = NotifySubscriberRequest, requestId = 329910
2025-07-24 09:55:39.892 [nacos-grpc-client-executor-************-21592] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329914
2025-07-24 09:55:39.894 [nacos-grpc-client-executor-************-21592] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329914
2025-07-24 09:55:39.895 [nacos-grpc-client-executor-************-21594] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329915
2025-07-24 09:55:39.896 [nacos-grpc-client-executor-************-21594] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329915
2025-07-24 09:55:39.897 [nacos-grpc-client-executor-************-21595] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Receive server push request, request = NotifySubscriberRequest, requestId = 329916
2025-07-24 09:55:39.897 [nacos-grpc-client-executor-************-21595] INFO  com.alibaba.nacos.common.remote.client:63 - [c2a9ceef-78b9-4663-9360-707fb5c8fb5d] Ack server push request, request = NotifySubscriberRequest, requestId = 329916
