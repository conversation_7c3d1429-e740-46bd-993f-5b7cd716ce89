package com.ideal.envc.model.dto;

import com.ideal.envc.model.enums.FileComparisonStrategy;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 文件比较请求DTO
 *
 * <AUTHOR>
 */
public class FileComparisonRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 源内容字符串（基线）
     */
    @NotBlank(message = "源内容不能为空")
    private String sourceContent;

    /**
     * 目标内容字符串
     */
    @NotBlank(message = "目标内容不能为空")
    private String targetContent;

    /**
     * 基线服务器名称
     */
    private String baselineServer;

    /**
     * 基线设备IP
     */
    private String baseServerIp;

    /**
     * 目标服务器名称
     */
    private String targetServer;

    /**
     * 目标设备IP
     */
    private String targetServerIp;

    /**
     * 比较描述
     */
    private String description;

    /**
     * 流程ID
     */
    private Long flowId;
    /**
     * 文件比较策略
     * 默认为MD5_ONLY（仅MD5比较）
     */
    private FileComparisonStrategy comparisonStrategy = FileComparisonStrategy.COMPREHENSIVE;

    public String getSourceContent() {
        return sourceContent;
    }

    public void setSourceContent(String sourceContent) {
        this.sourceContent = sourceContent;
    }

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    public String getBaselineServer() {
        return baselineServer;
    }

    public void setBaselineServer(String baselineServer) {
        this.baselineServer = baselineServer;
    }

    public String getTargetServer() {
        return targetServer;
    }

    public void setTargetServer(String targetServer) {
        this.targetServer = targetServer;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBaseServerIp() {
        return baseServerIp;
    }

    public void setBaseServerIp(String baseServerIp) {
        this.baseServerIp = baseServerIp;
    }

    public String getTargetServerIp() {
        return targetServerIp;
    }

    public void setTargetServerIp(String targetServerIp) {
        this.targetServerIp = targetServerIp;
    }

    public FileComparisonStrategy getComparisonStrategy() {
        return comparisonStrategy;
    }

    public void setComparisonStrategy(FileComparisonStrategy comparisonStrategy) {
        this.comparisonStrategy = comparisonStrategy != null ? comparisonStrategy : FileComparisonStrategy.MD5_ONLY;
    }

    @Override
    public String toString() {
        return "FileComparisonRequestDto{" +
                "sourceContent='" + sourceContent + '\'' +
                ", targetContent='" + targetContent + '\'' +
                ", baselineServer='" + baselineServer + '\'' +
                ", baseServerIp='" + baseServerIp + '\'' +
                ", targetServer='" + targetServer + '\'' +
                ", targetServerIp='" + targetServerIp + '\'' +
                ", description='" + description + '\'' +
                ", flowId=" + flowId +
                ", comparisonStrategy=" + comparisonStrategy +
                '}';
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }
}
