package com.ideal.envc.component;

import com.ideal.envc.model.enums.FileComparisonStrategy;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.service.impl.FileComparisonServiceImpl;
import org.mockito.Mockito;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件比较组件集成测试
 * 直接测试实际功能，不使用Mock
 *
 * <AUTHOR>
 */
public class FileComparisonComponentIntegrationTest {

    private FileComparisonComponent fileComparisonComponent;
    private String sourceContent;
    private String targetContent;

    @BeforeEach
    void setUp() {
        // 创建真实的组件实例
        FileComparisonServiceImpl fileComparisonService = new FileComparisonServiceImpl();
        // 创建SystemComputerMapper的Mock实例（集成测试中不需要真实的数据库连接）
        SystemComputerMapper systemComputerMapper = Mockito.mock(SystemComputerMapper.class);
        fileComparisonComponent = new FileComparisonComponent(fileComparisonService, systemComputerMapper);

        // 准备测试数据 - 基线内容
        sourceContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: 98f46ab6481d87c4d77e0e91a6dbc15f)\n" +
                       "README (size: 46.00 B, permissions: -rw-r--r--, MD5: 0f1123976b959ac5e8b89eb8c245c4bd)\n" +
                       "bin/java (size: 8.27 KB, permissions: -rwxr-xr-x, MD5: 9d5432654f4567e4e5076e6498471e8b)\n" +
                       "config/app.properties (size: 1.2 KB, permissions: -rw-r--r--, MD5: config_file_md5_hash_value)";

        // 准备测试数据 - 目标内容（包含各种差异情况）
        targetContent = "COPYRIGHT (size: 3.17 KB, permissions: -rw-r--r--, MD5: a762796b2a8989b8952b653a178607a1)\n" +  // 一致
                       "LICENSE (size: 40.00 B, permissions: -rw-r--r--, MD5: different_license_md5_value)\n" +           // 不一致
                       "bin/java (size: 8.27 KB, permissions: -rwxr-xr-x, MD5: 9d5432654f4567e4e5076e6498471e8b)\n" +    // 一致
                       "new_file.txt (size: 500.00 B, permissions: -rw-r--r--, MD5: new_file_md5_hash_value)";           // 多出
        // README 和 config/app.properties 在目标中缺失
    }

    @Test
    @DisplayName("集成测试 - 完整的文件比较流程")
    void testCompleteFileComparison() throws ContrastBusinessException {
        System.out.println("=== 开始文件比较集成测试 ===");
        
        // 执行比较
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器");

        // 验证基本统计信息
        assertNotNull(result, "比较结果不应为空");
        assertEquals(5, result.getTotalSourceFiles(), "基线文件总数应为5");
        assertEquals(4, result.getTotalTargetFiles(), "目标文件总数应为4");
        
        // 验证分类统计
        assertEquals(2, result.getConsistentCount(), "一致文件数应为2");
        assertEquals(1, result.getInconsistentCount(), "不一致文件数应为1");
        assertEquals(2, result.getMissingCount(), "缺失文件数应为2");
        assertEquals(1, result.getExtraCount(), "多出文件数应为1");

        // 打印详细结果
        System.out.println("比较结果统计：");
        System.out.println("- 基线文件总数：" + result.getTotalSourceFiles());
        System.out.println("- 目标文件总数：" + result.getTotalTargetFiles());
        System.out.println("- 一致文件：" + result.getConsistentCount() + "个");
        System.out.println("- 不一致文件：" + result.getInconsistentCount() + "个");
        System.out.println("- 缺失文件：" + result.getMissingCount() + "个");
        System.out.println("- 多出文件：" + result.getExtraCount() + "个");

        // 验证一致文件
        assertNotNull(result.getConsistentFiles(), "一致文件列表不应为空");
        assertEquals(2, result.getConsistentFiles().size(), "一致文件列表大小应为2");
        
        System.out.println("\n一致文件列表：");
        result.getConsistentFiles().forEach(file -> {
            System.out.println("- " + file.getFilePath() + " (MD5: " + file.getMd5() + ")");
            assertEquals("一致", file.getStatus());
        });

        // 验证不一致文件
        assertNotNull(result.getInconsistentFiles(), "不一致文件列表不应为空");
        assertEquals(1, result.getInconsistentFiles().size(), "不一致文件列表大小应为1");
        
        System.out.println("\n不一致文件列表：");
        result.getInconsistentFiles().forEach(file -> {
            System.out.println("- " + file.getFilePath() + " (MD5: " + file.getMd5() + ")");
            assertEquals("不一致", file.getStatus());
            assertEquals("LICENSE", file.getFilePath());
        });

        // 验证缺失文件
        assertNotNull(result.getMissingFiles(), "缺失文件列表不应为空");
        assertEquals(2, result.getMissingFiles().size(), "缺失文件列表大小应为2");
        
        System.out.println("\n缺失文件列表：");
        result.getMissingFiles().forEach(file -> {
            System.out.println("- " + file.getFilePath() + " (MD5: " + file.getMd5() + ")");
            assertEquals("缺失", file.getStatus());
        });

        // 验证多出文件
        assertNotNull(result.getExtraFiles(), "多出文件列表不应为空");
        assertEquals(1, result.getExtraFiles().size(), "多出文件列表大小应为1");
        
        System.out.println("\n多出文件列表：");
        result.getExtraFiles().forEach(file -> {
            System.out.println("- " + file.getFilePath() + " (MD5: " + file.getMd5() + ")");
            assertEquals("多出", file.getStatus());
            assertEquals("new_file.txt", file.getFilePath());
        });

        // 测试摘要生成
        String summary = fileComparisonComponent.getComparisonSummary(result);
        System.out.println("\n比较摘要：");
        System.out.println(summary);
        
        assertNotNull(summary, "摘要不应为空");
        assertTrue(summary.contains("基线文件总数：5"), "摘要应包含基线文件总数");
        assertTrue(summary.contains("目标文件总数：4"), "摘要应包含目标文件总数");
        assertTrue(summary.contains("一致文件：2个"), "摘要应包含一致文件数");

        System.out.println("\n=== 文件比较集成测试完成 ===");
    }

    @Test
    @DisplayName("测试输入验证功能")
    void testInputValidation() {
        System.out.println("=== 测试输入验证功能 ===");

        // 测试有效输入
        boolean validResult = fileComparisonComponent.validateInput(sourceContent, targetContent);
        assertTrue(validResult, "有效输入应该通过验证");
        System.out.println("✓ 有效输入验证通过");

        // 测试无效输入
        boolean invalidResult1 = fileComparisonComponent.validateInput("", targetContent);
        assertFalse(invalidResult1, "空源内容应该验证失败");
        System.out.println("✓ 空源内容验证失败（符合预期）");

        boolean invalidResult2 = fileComparisonComponent.validateInput(sourceContent, "");
        assertFalse(invalidResult2, "空目标内容应该验证失败");
        System.out.println("✓ 空目标内容验证失败（符合预期）");

        boolean invalidResult3 = fileComparisonComponent.validateInput(null, targetContent);
        assertFalse(invalidResult3, "null源内容应该验证失败");
        System.out.println("✓ null源内容验证失败（符合预期）");

        System.out.println("=== 输入验证功能测试完成 ===");
    }

    @Test
    @DisplayName("测试边界情况 - 空文件列表")
    void testEmptyFileList() {
        System.out.println("=== 测试边界情况 - 空文件列表 ===");

        String emptySource = "";
        String emptyTarget = "";

        // 这个测试会因为输入验证失败而无法执行比较
        boolean isValid = fileComparisonComponent.validateInput(emptySource, emptyTarget);
        assertFalse(isValid, "空文件列表应该验证失败");
        System.out.println("✓ 空文件列表验证失败（符合预期）");

        System.out.println("=== 边界情况测试完成 ===");
    }

    @Test
    @DisplayName("测试完全相同的文件列表")
    void testIdenticalFileLists() throws ContrastBusinessException {
        System.out.println("=== 测试完全相同的文件列表 ===");

        // 使用相同的内容进行比较
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceContent, sourceContent, "服务器A", "服务器B");

        // 验证结果
        assertEquals(5, result.getTotalSourceFiles(), "基线文件总数应为5");
        assertEquals(5, result.getTotalTargetFiles(), "目标文件总数应为5");
        assertEquals(5, result.getConsistentCount(), "所有文件都应该一致");
        assertEquals(0, result.getInconsistentCount(), "不应该有不一致文件");
        assertEquals(0, result.getMissingCount(), "不应该有缺失文件");
        assertEquals(0, result.getExtraCount(), "不应该有多出文件");

        System.out.println("✓ 完全相同文件列表比较结果正确");
        System.out.println("- 一致文件：" + result.getConsistentCount() + "个");
        System.out.println("- 其他类型文件：0个");

        System.out.println("=== 完全相同文件列表测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - MD5_ONLY比较策略")
    void testMD5OnlyComparisonStrategy() throws ContrastBusinessException {
        System.out.println("=== 开始MD5_ONLY比较策略集成测试 ===");

        // 准备测试数据 - 包含文件大小和权限不同但MD5相同的情况
        String sourceWithDiff = "file1.txt (size: 1024, permissions: -rw-r--r--, MD5: same_md5_hash)\n" +
                               "file2.txt (size: 2048, permissions: -rwxr-xr-x, MD5: different_md5_1)";

        String targetWithDiff = "file1.txt (size: 2048, permissions: -rwxrwxrwx, MD5: same_md5_hash)\n" +
                               "file2.txt (size: 2048, permissions: -rwxr-xr-x, MD5: different_md5_2)";

        // 执行比较 - 使用MD5_ONLY策略
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceWithDiff, targetWithDiff, "基线服务器", "目标服务器",
                FileComparisonStrategy.MD5_ONLY);

        // 验证结果
        assertNotNull(result, "比较结果不应为空");
        assertEquals(2, result.getTotalSourceFiles(), "基线文件总数应为2");
        assertEquals(2, result.getTotalTargetFiles(), "目标文件总数应为2");
        assertEquals(1, result.getConsistentCount(), "应该有1个一致文件（file1.txt，MD5相同）");
        assertEquals(1, result.getInconsistentCount(), "应该有1个不一致文件（file2.txt，MD5不同）");
        assertEquals(0, result.getMissingCount(), "不应该有缺失文件");
        assertEquals(0, result.getExtraCount(), "不应该有多出文件");

        // 验证备注信息
        assertEquals("文件一致", result.getConsistentFiles().get(0).getRemark());
        assertEquals("文件内容不一致，MD5值不同", result.getInconsistentFiles().get(0).getRemark());

        System.out.println("✓ MD5_ONLY策略下，file1.txt被认为一致（尽管大小和权限不同）");
        System.out.println("✓ file2.txt被认为不一致（MD5不同）");
        System.out.println("=== MD5_ONLY比较策略集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - COMPREHENSIVE比较策略")
    void testComprehensiveComparisonStrategy() throws ContrastBusinessException {
        System.out.println("=== 开始COMPREHENSIVE比较策略集成测试 ===");

        // 准备测试数据 - 包含各种不同类型的差异
        String sourceWithDiff = "file1.txt (size: 1024, permissions: -rw-r--r--, MD5: same_md5_hash)\n" +
                               "file2.txt (size: 2048, permissions: -rwxr-xr-x, MD5: same_md5_hash2)\n" +
                               "file3.txt (size: 512, permissions: -rw-r--r--, MD5: same_md5_hash3)\n" +
                               "file4.txt (size: 256, permissions: -rw-r--r--, MD5: same_md5_hash4)";

        String targetWithDiff = "file1.txt (size: 2048, permissions: -rw-r--r--, MD5: same_md5_hash)\n" +      // 大小不同
                               "file2.txt (size: 2048, permissions: -rw-rw-rw-, MD5: same_md5_hash2)\n" +     // 权限不同
                               "file3.txt (size: 512, permissions: -rw-r--r--, MD5: different_md5)\n" +       // MD5不同
                               "file4.txt (size: 256, permissions: -rw-r--r--, MD5: same_md5_hash4)";        // 完全一致

        // 执行比较 - 使用COMPREHENSIVE策略
        FileComparisonResultDto result = fileComparisonComponent.compareFiles(
                sourceWithDiff, targetWithDiff, "基线服务器", "目标服务器",
                FileComparisonStrategy.COMPREHENSIVE);

        // 验证结果
        assertNotNull(result, "比较结果不应为空");
        assertEquals(4, result.getTotalSourceFiles(), "基线文件总数应为4");
        assertEquals(4, result.getTotalTargetFiles(), "目标文件总数应为4");
        assertEquals(1, result.getConsistentCount(), "应该有1个一致文件（file4.txt）");
        assertEquals(3, result.getInconsistentCount(), "应该有3个不一致文件");
        assertEquals(0, result.getMissingCount(), "不应该有缺失文件");
        assertEquals(0, result.getExtraCount(), "不应该有多出文件");

        // 验证不一致文件的备注信息
        boolean hasFileSizeDiff = result.getInconsistentFiles().stream()
                .anyMatch(file -> file.getRemark().contains("文件大小不同"));
        boolean hasPermissionDiff = result.getInconsistentFiles().stream()
                .anyMatch(file -> file.getRemark().contains("权限不同"));
        boolean hasMd5Diff = result.getInconsistentFiles().stream()
                .anyMatch(file -> file.getRemark().contains("MD5值不同"));

        assertTrue(hasFileSizeDiff, "应该有文件大小不同的提示");
        assertTrue(hasPermissionDiff, "应该有权限不同的提示");
        assertTrue(hasMd5Diff, "应该有MD5值不同的提示");

        System.out.println("✓ COMPREHENSIVE策略下，只有file4.txt被认为一致");
        System.out.println("✓ file1.txt被认为不一致（文件大小不同）");
        System.out.println("✓ file2.txt被认为不一致（权限不同）");
        System.out.println("✓ file3.txt被认为不一致（MD5值不同）");
        System.out.println("=== COMPREHENSIVE比较策略集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - 比较策略对比")
    void testComparisonStrategyComparison() throws ContrastBusinessException {
        System.out.println("=== 开始比较策略对比集成测试 ===");

        // 准备测试数据 - 文件大小不同但MD5相同
        String sourceContent = "test.txt (size: 1024, permissions: -rw-r--r--, MD5: same_md5_value)";
        String targetContent = "test.txt (size: 2048, permissions: -rwxrwxrwx, MD5: same_md5_value)";

        // 使用MD5_ONLY策略
        FileComparisonResultDto md5OnlyResult = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器",
                FileComparisonStrategy.MD5_ONLY);

        // 使用COMPREHENSIVE策略
        FileComparisonResultDto comprehensiveResult = fileComparisonComponent.compareFiles(
                sourceContent, targetContent, "基线服务器", "目标服务器",
                FileComparisonStrategy.COMPREHENSIVE);

        // 验证MD5_ONLY结果
        assertEquals(1, md5OnlyResult.getConsistentCount(), "MD5_ONLY策略应该认为文件一致");
        assertEquals(0, md5OnlyResult.getInconsistentCount(), "MD5_ONLY策略不应该有不一致文件");

        // 验证COMPREHENSIVE结果
        assertEquals(0, comprehensiveResult.getConsistentCount(), "COMPREHENSIVE策略不应该有一致文件");
        assertEquals(1, comprehensiveResult.getInconsistentCount(), "COMPREHENSIVE策略应该认为文件不一致");

        // 验证备注信息
        String comprehensiveRemark = comprehensiveResult.getInconsistentFiles().get(0).getRemark();
        assertTrue(comprehensiveRemark.contains("文件大小不同"), "COMPREHENSIVE策略应该提示文件大小不同");
        assertTrue(comprehensiveRemark.contains("权限不同"), "COMPREHENSIVE策略应该提示权限不同");

        System.out.println("✓ 策略对比验证完成：");
        System.out.println("  - MD5_ONLY: 一致文件=" + md5OnlyResult.getConsistentCount() +
                          ", 不一致文件=" + md5OnlyResult.getInconsistentCount());
        System.out.println("  - COMPREHENSIVE: 一致文件=" + comprehensiveResult.getConsistentCount() +
                          ", 不一致文件=" + comprehensiveResult.getInconsistentCount());
        System.out.println("=== 比较策略对比集成测试完成 ===");
    }

    @Test
    @DisplayName("集成测试 - 带比较策略的导出功能")
    void testExportWithComparisonStrategy() throws ContrastBusinessException {
        System.out.println("=== 开始带比较策略的导出功能集成测试 ===");

        // 准备响应对象
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行比较并导出 - 使用COMPREHENSIVE策略
        fileComparisonComponent.compareAndExport(
                sourceContent, targetContent,
                "192.168.1.100", "基线服务器",
                "192.168.1.101", "目标服务器",
                FileComparisonStrategy.COMPREHENSIVE,
                response);

        // 验证响应
        assertNotNull(response.getContentType(), "响应Content-Type不应为空");
        assertTrue(response.getContentType().contains("spreadsheetml"), "应该是Excel格式");
        assertTrue(response.getContentLength() > 0, "响应内容长度应该大于0");

        System.out.println("✓ 导出功能验证完成");
        System.out.println("  - Content-Type: " + response.getContentType());
        System.out.println("  - Content-Length: " + response.getContentLength());
        System.out.println("=== 带比较策略的导出功能集成测试完成 ===");
    }
}
